import { Users, Award, Globe, Clock } from "lucide-react"
import Image from "next/image"

export default function About() {
  const features = [
    {
      icon: Users,
      title: "Expert Team",
      description:
        "We are a team of professionals with extensive experience in event management and conference organization.",
    },
    {
      icon: Award,
      title: "Quality Excellence",
      description: "Delivering world-class events with attention to detail and commitment to exceeding expectations.",
    },
    {
      icon: Globe,
      title: "Global Reach",
      description: "Managing national and international events with seamless coordination and cultural sensitivity.",
    },
    {
      icon: Clock,
      title: "Timely Execution",
      description: "Precise planning and flawless execution ensuring your events run smoothly and on schedule.",
    },
  ]

  return (
    <section id="about" className="py-24 bg-white overflow-x-hidden">
      <div className="container mx-auto px-4 max-w-full">
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Who{" "}
            <span className="bg-gradient-to-r from-orange-600 to-rose-600 bg-clip-text text-transparent">We Are?</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed px-2">
            We are a Team of Professionals dedicated to creating exceptional events that leave lasting impressions. With
            years of experience in conference management, exhibitions, and corporate events, we bring expertise,
            creativity, and precision to every project we undertake.
          </p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8 w-full">
          {features.map((feature, index) => (
            <div
              key={index}
              className="text-center group hover:transform hover:scale-105 transition-all duration-500 animate-fade-in-up px-2"
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <div className="w-16 h-16 bg-gradient-to-r from-orange-100 to-rose-100 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:from-orange-200 group-hover:to-rose-200 transition-all duration-300">
                <feature.icon className="w-8 h-8 text-orange-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">{feature.title}</h3>
              <p className="text-gray-600 leading-relaxed">{feature.description}</p>
            </div>
          ))}
        </div>

        <div className="mt-16 bg-gradient-to-r from-orange-50 to-rose-50 rounded-3xl p-6 sm:p-8 lg:p-12 mx-2 sm:mx-0">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8 items-center">
            <div>
              <h3 className="text-3xl font-bold text-gray-900 mb-6">Our Mission</h3>
              <p className="text-gray-600 leading-relaxed mb-6">
                To be the leading event management company that transforms ideas into extraordinary experiences. We
                strive to create memorable events that connect people, inspire innovation, and drive business success.
              </p>
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center">
                  <div className="text-3xl font-bold text-orange-600">500+</div>
                  <div className="text-gray-600">Events Managed</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-rose-600">50+</div>
                  <div className="text-gray-600">Happy Clients</div>
                </div>
              </div>
            </div>
            <div className="relative w-full industry-leader-tag-container">
              <div className="aspect-square rounded-3xl overflow-hidden shadow-lg relative w-full h-full">
                <Image
                  src="/images/mission/1.jpg"
                  alt="Our Mission - Orange Rose Events"
                  width={500}
                  height={500}
                  className="w-full h-full object-cover hover:scale-105 transition-transform duration-500 relative z-0"
                />
                {/* Industry Leader Tag - positioned at bottom center INSIDE image container */}
                <div className="industry-leader-tag">
                  <div className="text-center leading-tight">
                    <div className="font-bold">Industry Leader</div>
                    <div style={{ fontSize: '10px', opacity: 0.9 }}>Since 2009</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>  
    </section>
  )
}
