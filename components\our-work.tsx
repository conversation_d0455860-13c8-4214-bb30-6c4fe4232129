import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Award, Users, Globe, TrendingUp, Star, CheckCircle } from "lucide-react"

export default function OurWork() {
  const achievements = [
    {
      icon: Award,
      number: "500+",
      label: "Events Managed",
      description: "Successfully organized events across various industries",
    },
    {
      icon: Users,
      number: "100K+",
      label: "Attendees Served",
      description: "Connected professionals and industry leaders worldwide",
    },
    {
      icon: Globe,
      number: "25+",
      label: "Cities Covered",
      description: "Pan-India presence with international collaborations",
    },
    {
      icon: TrendingUp,
      number: "98%",
      label: "Client Satisfaction",
      description: "Consistently exceeding client expectations",
    },
  ]

  const capabilities = [
    "End-to-end event planning and execution",
    "International speaker coordination",
    "Advanced registration and ticketing systems",
    "Live streaming and hybrid event solutions",
    "Professional audio-visual production",
    "Comprehensive marketing and promotion",
    "Real-time event analytics and reporting",
    "Post-event follow-up and engagement",
  ]

  const industries = [
    "Healthcare & Pharmaceuticals",
    "Technology & Innovation",
    "Finance & Banking",
    "Education & Research",
    "Manufacturing & Industrial",
    "Government & Public Sector",
  ]

  return (
    <section id="work" className="py-24 bg-gradient-to-br from-gray-50 to-orange-50 overflow-x-hidden">
      <div className="container mx-auto px-4 max-w-full overflow-x-hidden">
        <div className="text-center mb-20">
          <div className="inline-flex items-center gap-2 bg-orange-100 rounded-full px-6 py-2 text-sm font-medium text-orange-600 mb-6">
            <Star className="w-4 h-4" />
            Our Excellence
          </div>
          <h2 className="text-4xl lg:text-6xl font-bold text-gray-900 mb-8">
            Our <span className="bg-gradient-to-r from-orange-600 to-rose-600 bg-clip-text text-transparent">Work</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            With years of experience and a proven track record, we have established ourselves as a leading event
            management company, delivering exceptional results across diverse industries and event formats.
          </p>
        </div>

        {/* Achievements Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-20">
          {achievements.map((achievement, index) => (
            <Card
              key={index}
              className="text-center group hover:shadow-xl transition-all duration-500 transform hover:-translate-y-2 bg-white/80 backdrop-blur-sm border-0 animate-fade-in-up"
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <CardContent className="p-8">
                <div className="w-16 h-16 bg-gradient-to-r from-orange-500 to-rose-500 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                  <achievement.icon className="w-8 h-8 text-white" />
                </div>
                <div className="text-4xl font-bold text-gray-900 mb-2">{achievement.number}</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">{achievement.label}</h3>
                <p className="text-gray-600 text-sm leading-relaxed">{achievement.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="w-full max-w-full overflow-hidden">
          {/* Capabilities */}
          <div className="animate-fade-in-up w-full max-w-full overflow-hidden mb-8" style={{ animationDelay: "400ms" }}>
            <h3 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-6 sm:mb-8 px-2">Our Capabilities</h3>
            <div className="space-y-2 sm:space-y-4 w-full max-w-full overflow-hidden px-2">
              {capabilities.map((capability, index) => (
                <div
                  key={index}
                  className="w-full max-w-full overflow-hidden"
                  style={{
                    maxWidth: 'calc(100vw - 4rem)',
                    overflow: 'hidden',
                    boxSizing: 'border-box'
                  }}
                >
                  <div className="flex items-start gap-2 p-2 bg-white/80 rounded-lg hover:bg-white transition-all duration-300 w-full max-w-full overflow-hidden">
                    <CheckCircle className="w-4 h-4 text-green-500 mt-1 flex-shrink-0" />
                    <div
                      className="text-gray-700 leading-relaxed text-xs flex-1"
                      style={{
                        wordWrap: 'break-word',
                        wordBreak: 'break-word',
                        overflowWrap: 'break-word',
                        maxWidth: 'calc(100% - 2rem)',
                        whiteSpace: 'normal',
                        fontSize: '12px',
                        lineHeight: '1.4'
                      }}
                    >
                      {capability}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Industries */}
          <div className="animate-fade-in-up w-full max-w-full overflow-hidden" style={{ animationDelay: "600ms" }}>
            <h3 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-6 sm:mb-8 px-2">Industries We Serve</h3>
            <div className="space-y-2 sm:space-y-4 w-full max-w-full overflow-hidden px-2">
              {industries.map((industry, index) => (
                <div
                  key={index}
                  className="w-full max-w-full overflow-hidden"
                  style={{
                    maxWidth: 'calc(100vw - 4rem)',
                    overflow: 'hidden',
                    boxSizing: 'border-box'
                  }}
                >
                  <div className="bg-white/80 rounded-lg p-2 hover:bg-white transition-all duration-300 w-full max-w-full overflow-hidden">
                    <div className="flex items-center gap-2 w-full max-w-full overflow-hidden">
                      <div className="w-2 h-2 bg-gradient-to-r from-orange-500 to-rose-500 rounded-full flex-shrink-0"></div>
                      <div
                        className="font-semibold text-gray-900 text-xs flex-1"
                        style={{
                          wordWrap: 'break-word',
                          wordBreak: 'break-word',
                          overflowWrap: 'break-word',
                          maxWidth: 'calc(100% - 1rem)',
                          whiteSpace: 'normal',
                          fontSize: '12px',
                          lineHeight: '1.4'
                        }}
                      >
                        {industry}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Call to Action */}
        <div className="text-center mt-20">
          <div className="bg-gradient-to-r from-orange-500 to-rose-500 rounded-3xl p-12 text-white">
            <h3 className="text-3xl font-bold mb-6">Ready to Create Something Amazing?</h3>
            <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
              Let's discuss your next event and how we can bring your vision to life with our expertise and creativity.
            </p>
            <Button
              size="lg"
              className="bg-white text-orange-600 hover:bg-gray-100 px-10 py-4 rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg font-semibold"
            >
              Start Your Project
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}
